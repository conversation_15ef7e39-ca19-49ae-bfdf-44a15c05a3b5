2025-05-29 16:21:37.090 | INFO     | uvicorn.server:_serve:83 - Started server process [367455]
2025-05-29 16:21:37.091 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:21:37.091 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:21:37.091 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:21:37.091 | INFO     | app.helpers.face_detector:_get_face_detector:215 - Creating global face detector instance
2025-05-29 16:21:37.091 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:21:38.129 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 1.04s, detection size: (640, 640))
2025-05-29 16:21:38.213 | INFO     | app.helpers.face_detector:_warmup_model:103 - Model warmup completed successfully
2025-05-29 16:21:38.214 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:21:38.214 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:21:42.638 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:42.638 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:42.752 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:21:42.752 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:43.178 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:21:43.178 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:21:43.283 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:21:43.283 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:21:43.284 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:21:43.284 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:53572 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:21:46.462 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:21:46.462 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:21:47.682 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:21:47.683 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:21:49.245 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:21:49.245 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:21:49.369 | INFO     | app.helpers.face_detector:detect_faces:183 - No faces detected in image
2025-05-29 16:21:49.369 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:21:49.370 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:21:49.370 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:53572 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:21:52.170 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:52.170 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:52.218 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:21:52.218 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:21:52.616 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:21:52.616 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:21:52.774 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:21:52.774 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:21:52.775 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:21:52.776 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:53572 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:21:58.669 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://www.google.com/
2025-05-29 16:21:58.669 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://www.google.com/
2025-05-29 16:21:58.781 | WARNING  | app.services.face_validation_service:validate_face_from_url:36 - URL validation failed for sdfsdfsd: URL does not point to an image (content-type: text/html; charset=iso-8859-1)
2025-05-29 16:21:58.781 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:21:58.782 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:51416 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fwww.google.com HTTP/1.1" 200
2025-05-29 16:22:17.373 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://st.adda247.com/https://wpassets.adda247.com/wp-content/uploads/multisite/sites/5/2022/04/13080458/modi-6-16487841703x2-1.jpg
2025-05-29 16:22:17.373 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://st.adda247.com/https://wpassets.adda247.com/wp-content/uploads/multisite/sites/5/2022/04/13080458/modi-6-16487841703x2-1.jpg
2025-05-29 16:22:17.538 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:17.538 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://st.adda247.com/https://wpassets.adda247.com/wp-content/uploads/multisite/sites/5/2022/04/13080458/modi-6-16487841703x2-1.jpg
2025-05-29 16:22:17.903 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (800, 1200, 3)
2025-05-29 16:22:17.904 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:17.987 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:22:17.987 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:22:17.987 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:22:17.987 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:56490 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fst.adda247.com%2Fhttps%3A%2F%2Fwpassets.adda247.com%2Fwp-content%2Fuploads%2Fmultisite%2Fsites%2F5%2F2022%2F04%2F13080458%2Fmodi-6-16487841703x2-1.jpg HTTP/1.1" 200
2025-05-29 16:22:30.054 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/OIP._KepakEBIlgIkeSOnMdyyQHaHa?rs=1&pid=ImgDetMain
2025-05-29 16:22:30.054 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/OIP._KepakEBIlgIkeSOnMdyyQHaHa?rs=1&pid=ImgDetMain
2025-05-29 16:22:30.299 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:30.300 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/OIP._KepakEBIlgIkeSOnMdyyQHaHa?rs=1&pid=ImgDetMain
2025-05-29 16:22:30.516 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (474, 474, 3)
2025-05-29 16:22:30.517 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:30.597 | INFO     | app.helpers.face_detector:detect_faces:181 - Detected 1 face(s) in image
2025-05-29 16:22:30.598 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:22:30.598 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:22:30.598 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:56412 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIP._KepakEBIlgIkeSOnMdyyQHaHa%3Frs%3D1%26pid%3DImgDetMain HTTP/1.1" 200
2025-05-29 16:22:42.624 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://images.examples.com/wp-content/uploads/2024/05/Object.png
2025-05-29 16:22:42.624 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://images.examples.com/wp-content/uploads/2024/05/Object.png
2025-05-29 16:22:42.694 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:42.694 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://images.examples.com/wp-content/uploads/2024/05/Object.png
2025-05-29 16:22:42.790 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (675, 1200, 3)
2025-05-29 16:22:42.790 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:42.876 | INFO     | app.helpers.face_detector:detect_faces:183 - No faces detected in image
2025-05-29 16:22:42.876 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:22:42.876 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:22:42.876 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:43026 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fimages.examples.com%2Fwp-content%2Fuploads%2F2024%2F05%2FObject.png HTTP/1.1" 200
2025-05-29 16:22:52.737 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://vocabularyan.com/wp-content/uploads/2023/03/100-Classroom-Object-Names-with-Picture-.png
2025-05-29 16:22:52.737 | INFO     | app.services.face_validation_service:validate_face_from_url:30 - Starting face validation for file sdfsdfsd from URL: https://vocabularyan.com/wp-content/uploads/2023/03/100-Classroom-Object-Names-with-Picture-.png
2025-05-29 16:22:52.814 | INFO     | app.services.face_validation_service:validate_face_from_url:41 - URL validation successful for sdfsdfsd
2025-05-29 16:22:52.814 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://vocabularyan.com/wp-content/uploads/2023/03/100-Classroom-Object-Names-with-Picture-.png
2025-05-29 16:22:52.894 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (480, 960, 3)
2025-05-29 16:22:52.894 | INFO     | app.services.face_validation_service:validate_face_from_url:51 - Image download successful for sdfsdfsd
2025-05-29 16:22:53.059 | INFO     | app.helpers.face_detector:detect_faces:183 - No faces detected in image
2025-05-29 16:22:53.059 | INFO     | app.services.face_validation_service:validate_face_from_url:63 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:22:53.059 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:22:53.059 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45356 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fvocabularyan.com%2Fwp-content%2Fuploads%2F2023%2F03%2F100-Classroom-Object-Names-with-Picture-.png HTTP/1.1" 200
2025-05-29 16:22:54.849 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45356 - "GET /docs HTTP/1.1" 200
2025-05-29 16:22:54.934 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45356 - "GET /openapi.json HTTP/1.1" 200
2025-05-29 16:23:30.440 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:23:30.540 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:23:30.540 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:23:30.541 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:23:30.541 | INFO     | uvicorn.server:_serve:93 - Finished server process [367455]
2025-05-29 16:23:31.381 | INFO     | uvicorn.server:_serve:83 - Started server process [368966]
2025-05-29 16:23:31.381 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:23:31.382 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:23:31.382 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:23:31.382 | INFO     | app.helpers.face_detector:_get_face_detector:215 - Creating global face detector instance
2025-05-29 16:23:31.382 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:23:32.060 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.68s, detection size: (640, 640))
2025-05-29 16:23:32.127 | INFO     | app.helpers.face_detector:_warmup_model:103 - Model warmup completed successfully
2025-05-29 16:23:32.128 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:23:32.128 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:23:33.643 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:23:33.745 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:23:33.745 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:23:33.745 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:23:33.745 | INFO     | uvicorn.server:_serve:93 - Finished server process [368966]
2025-05-29 16:30:19.957 | INFO     | uvicorn.server:_serve:83 - Started server process [373032]
2025-05-29 16:30:19.957 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:30:19.957 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:30:19.957 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:30:19.958 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:30:19.958 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:30:20.693 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.74s, detection size: (640, 640))
2025-05-29 16:30:20.784 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:30:20.784 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:30:20.784 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:30:42.175 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 960x480 (1.3MB)
2025-05-29 16:30:42.503 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✗ (0 faces)
2025-05-29 16:30:42.504 | INFO     | app.services.face_validation_service:validate_face_from_url:62 - Validation complete [sdfsd]: ✗ faces (0.49s)
2025-05-29 16:30:42.504 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:39276 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fvocabularyan.com%2Fwp-content%2Fuploads%2F2023%2F03%2F100-Classroom-Object-Names-with-Picture-.png HTTP/1.1" 200
2025-05-29 16:31:02.445 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 3586x3586 -> 1024x1024
2025-05-29 16:31:02.626 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x1024 (3.0MB)
2025-05-29 16:31:02.712 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:31:02.712 | INFO     | app.services.face_validation_service:validate_face_from_url:62 - Validation complete [sdfsd]: ✓ faces (8.59s)
2025-05-29 16:31:02.712 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:57114 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DvjpIMLnhfgqZkQ%26riu%3Dhttp%253a%252f%252fwww.metropixie.com%252fwp-content%252fuploads%252f2016%252f02%252fDepositphotos_80959566_original.jpg%26ehk%3DlV0Fk1JVq%252flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%253d%26risl%3D1%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:32:20.717 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:32:20.818 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:32:20.819 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:32:20.819 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:32:20.819 | INFO     | uvicorn.server:_serve:93 - Finished server process [373032]
2025-05-29 16:32:21.687 | INFO     | uvicorn.server:_serve:83 - Started server process [374425]
2025-05-29 16:32:21.687 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:32:21.687 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:32:21.687 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:32:21.687 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:32:21.687 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:32:22.418 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.73s, detection size: (640, 640))
2025-05-29 16:32:22.527 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:32:22.528 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:32:22.528 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:32:23.134 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:32:23.237 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:32:23.237 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:32:23.237 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:32:23.237 | INFO     | uvicorn.server:_serve:93 - Finished server process [374425]
2025-05-29 16:32:46.253 | INFO     | uvicorn.server:_serve:83 - Started server process [374793]
2025-05-29 16:32:46.254 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:32:46.254 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:32:46.254 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:32:46.254 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:32:46.254 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:32:47.088 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.83s, detection size: (640, 640))
2025-05-29 16:32:47.415 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:32:47.417 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:32:47.418 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:32:49.187 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/R.********************************?rik=vjpIMLnhfgqZkQ&riu=http%3a%2f%2fwww.metropixie.com%2fwp-content%2fuploads%2f2016%2f02%2fDepositphotos_80959566_original.jpg&ehk=lV0Fk1JVq%2flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%3d&risl=1&pid=ImgRaw&r=0
2025-05-29 16:32:49.777 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 3586x3586 -> 1024x1024
2025-05-29 16:32:49.957 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x1024 (3.0MB)
2025-05-29 16:32:50.029 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:32:50.029 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.84s)
2025-05-29 16:32:50.029 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49100 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DvjpIMLnhfgqZkQ%26riu%3Dhttp%253a%252f%252fwww.metropixie.com%252fwp-content%252fuploads%252f2016%252f02%252fDepositphotos_80959566_original.jpg%26ehk%3DlV0Fk1JVq%252flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%253d%26risl%3D1%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:32:54.366 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/R.********************************?rik=vjpIMLnhfgqZkQ&riu=http%3a%2f%2fwww.metropixie.com%2fwp-content%2fuploads%2f2016%2f02%2fDepositphotos_80959566_original.jpg&ehk=lV0Fk1JVq%2flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%3d&risl=1&pid=ImgRaw&r=0
2025-05-29 16:32:55.014 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 3586x3586 -> 1024x1024
2025-05-29 16:32:55.174 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x1024 (3.0MB)
2025-05-29 16:32:55.234 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:32:55.235 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.87s)
2025-05-29 16:32:55.235 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49100 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DvjpIMLnhfgqZkQ%26riu%3Dhttp%253a%252f%252fwww.metropixie.com%252fwp-content%252fuploads%252f2016%252f02%252fDepositphotos_80959566_original.jpg%26ehk%3DlV0Fk1JVq%252flmLz5qIRfDo3z9hACqqaOysMck6pAcWak%253d%26risl%3D1%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:33:02.667 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/OIP.HFgIRFK8_DuDhZ7RgaqO8QHaHa?w=626&h=626&rs=1&pid=ImgDetMain
2025-05-29 16:33:02.941 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 626x626 (1.1MB)
2025-05-29 16:33:03.025 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:33:03.025 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.36s)
2025-05-29 16:33:03.026 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:51170 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIP.HFgIRFK8_DuDhZ7RgaqO8QHaHa%3Fw%3D626%26h%3D626%26rs%3D1%26pid%3DImgDetMain HTTP/1.1" 200
2025-05-29 16:34:03.620 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://th.bing.com/th/id/OIP.Dc8yFXYSTaVk5hS6peyBhgHaE8?rs=1&pid=ImgDetMain
2025-05-29 16:34:03.960 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 474x316 (0.4MB)
2025-05-29 16:34:04.029 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:34:04.030 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (0.41s)
2025-05-29 16:34:04.031 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:44408 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIP.Dc8yFXYSTaVk5hS6peyBhgHaE8%3Frs%3D1%26pid%3DImgDetMain HTTP/1.1" 200
2025-05-29 16:34:20.784 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://wallpaperaccess.com/full/2082932.jpg
2025-05-29 16:34:26.255 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 8688x5792 -> 1024x682
2025-05-29 16:34:26.826 | INFO     | app.helpers.image_downloader:download_image_to_memory:104 - Image loaded: 1024x682 (2.0MB)
2025-05-29 16:34:26.904 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:34:26.904 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (6.12s)
2025-05-29 16:34:26.904 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:34058 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fwallpaperaccess.com%2Ffull%2F2082932.jpg HTTP/1.1" 200
2025-05-29 16:34:34.520 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:34:34.621 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:34:34.621 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:34:34.621 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:34:34.621 | INFO     | uvicorn.server:_serve:93 - Finished server process [374793]
2025-05-29 16:38:07.851 | INFO     | uvicorn.server:_serve:83 - Started server process [378318]
2025-05-29 16:38:07.851 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:38:07.851 | INFO     | main:lifespan:26 - Starting face validation service...
2025-05-29 16:38:07.851 | INFO     | main:lifespan:30 - Preloading face detection model...
2025-05-29 16:38:07.851 | INFO     | app.helpers.face_detector:_get_face_detector:227 - Creating global face detector instance
2025-05-29 16:38:07.851 | INFO     | app.helpers.face_detector:_initialize_model:55 - Starting InsightFace model initialization...
2025-05-29 16:38:08.636 | INFO     | app.helpers.face_detector:_initialize_model:70 - InsightFace detector initialized successfully with CPU providers (load time: 0.78s, detection size: (640, 640))
2025-05-29 16:38:08.812 | INFO     | app.helpers.face_detector:_warmup_model:107 - Model warmup completed successfully
2025-05-29 16:38:08.812 | INFO     | main:lifespan:32 - Face detection model preloaded successfully
2025-05-29 16:38:08.812 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:38:11.177 | INFO     | app.api.face_routes:validate_face:24 - Face validation request - ID: sdfsd, URL: https://wallpaperaccess.com/full/2082932.jpg
2025-05-29 16:38:14.096 | INFO     | app.helpers.image_downloader:download_image_to_memory:91 - Optimizing large download: 8688x5792 -> 1024x682
2025-05-29 16:38:14.523 | INFO     | app.helpers.image_downloader:download_image_to_memory:114 - Image loaded: 1024x682 (2.0MB)
2025-05-29 16:38:14.618 | INFO     | app.helpers.face_detector:detect_faces:193 - Face detection: ✓ (1 faces)
2025-05-29 16:38:14.618 | INFO     | app.services.face_validation_service:validate_face_from_url:61 - Validation complete [sdfsd]: ✓ faces (3.44s)
2025-05-29 16:38:14.619 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:50800 - "GET /face/validate?fileId=sdfsd&fileUrl=https%3A%2F%2Fwallpaperaccess.com%2Ffull%2F2082932.jpg HTTP/1.1" 200
2025-05-29 16:39:33.331 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:39:33.431 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:39:33.432 | INFO     | main:lifespan:43 - Shutting down face validation service...
2025-05-29 16:39:33.432 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:39:33.432 | INFO     | uvicorn.server:_serve:93 - Finished server process [378318]
