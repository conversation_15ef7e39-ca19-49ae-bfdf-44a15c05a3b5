# Caching System Documentation

The face validation service includes a comprehensive caching system to improve performance by avoiding repeated network requests and processing operations for the same files.

## Overview

The caching system provides three levels of caching:

1. **Image Download Caching** - Caches downloaded images from URLs
2. **Face Detection Caching** - Caches face detection results
3. **Image Preprocessing Caching** - Caches preprocessed images

## Configuration

Caching is controlled through environment variables:

### Core Cache Settings
- `CACHE_ENABLED=true` - Enable/disable caching (default: true)
- `CACHE_TYPE=memory` - Cache backend type: `memory` or `disk` (default: memory)
- `CACHE_MAX_SIZE_MB=500` - Maximum cache size in MB (default: 500)
- `CACHE_TTL_SECONDS=3600` - Time-to-live for cache entries in seconds (default: 1 hour)
- `CACHE_DIRECTORY=./cache` - Directory for disk cache (default: ./cache)

### Feature-Specific Cache Settings
- `CACHE_IMAGE_DOWNLOADS=true` - Cache downloaded images (default: true)
- `CACHE_FACE_DETECTION=true` - Cache face detection results (default: true)
- `CACHE_IMAGE_PROCESSING=true` - Cache preprocessed images (default: true)

## Cache Types

### Memory Cache
- **Pros**: Fastest access, no disk I/O
- **Cons**: Limited by available RAM, lost on restart
- **Best for**: Development, small deployments

### Disk Cache
- **Pros**: Persistent across restarts, larger capacity
- **Cons**: Slower than memory, requires disk space
- **Best for**: Production deployments with limited RAM

## Cache Keys

Cache keys are generated using SHA-256 hashes of:

1. **Image Downloads**: URL + download parameters
2. **Face Detection**: Image content hash + image dimensions
3. **Image Preprocessing**: Image content hash + processing parameters

## API Endpoints

### Get Cache Status
```
GET /face/cache/status
```

Returns cache statistics including:
- Cache enabled status
- Cache type (memory/disk)
- Number of entries
- Size utilization
- Hit/miss ratios (if available)

Example response:
```json
{
  "status": "success",
  "cache": {
    "enabled": true,
    "type": "memory",
    "entries": 15,
    "size_mb": 45.2,
    "max_size_mb": 500,
    "utilization": 0.09
  }
}
```

### Clear Cache
```
POST /face/cache/clear
```

Clears all cache entries.

Example response:
```json
{
  "status": "success",
  "message": "Cache cleared successfully"
}
```

## Performance Benefits

### Image Download Caching
- **First request**: Downloads image from network (~1-3 seconds)
- **Subsequent requests**: Loads from cache (~0.01-0.1 seconds)
- **Speedup**: 10-300x faster

### Face Detection Caching
- **First request**: Performs face detection (~0.1-1 seconds)
- **Subsequent requests**: Loads result from cache (~0.001-0.01 seconds)
- **Speedup**: 100-1000x faster

### Image Preprocessing Caching
- **First request**: Resizes and converts image (~0.05-0.5 seconds)
- **Subsequent requests**: Loads processed image from cache (~0.001-0.01 seconds)
- **Speedup**: 50-500x faster

## Cache Eviction

The cache uses LRU (Least Recently Used) eviction when:
- Cache size exceeds `CACHE_MAX_SIZE_MB`
- Entries exceed their TTL (`CACHE_TTL_SECONDS`)

## Monitoring

### Logs
Cache operations are logged at DEBUG level:
- Cache hits: "Image loaded from cache", "Face detection result loaded from cache"
- Cache misses: Normal processing logs
- Cache storage: "Cached downloaded image", "Cached face detection result"

### Metrics
Use the `/face/cache/status` endpoint to monitor:
- Cache utilization
- Number of entries
- Memory usage

## Testing

Run the cache test suite:
```bash
python test_caching.py
```

This tests:
- Basic cache functionality
- Image download caching
- Face detection caching
- Image preprocessing caching

## Best Practices

1. **Memory Cache**: Use for development and small deployments
2. **Disk Cache**: Use for production with limited RAM
3. **TTL Settings**: 
   - Short TTL (5-15 minutes) for frequently changing content
   - Long TTL (1-24 hours) for stable content
4. **Size Limits**: Set based on available system resources
5. **Monitoring**: Regularly check cache status and adjust settings

## Troubleshooting

### Cache Not Working
1. Check `CACHE_ENABLED=true`
2. Verify cache type is supported
3. Check disk space (for disk cache)
4. Review logs for cache errors

### High Memory Usage
1. Reduce `CACHE_MAX_SIZE_MB`
2. Reduce `CACHE_TTL_SECONDS`
3. Switch to disk cache
4. Clear cache manually

### Poor Performance
1. Increase cache size if utilization is high
2. Check cache hit rates in logs
3. Verify cache keys are consistent
4. Consider switching cache types

## Implementation Details

The caching system is implemented in `app/helpers/cache_manager.py` and integrated into:
- `app/helpers/image_downloader.py` - Image download caching
- `app/helpers/face_detector.py` - Face detection caching
- `app/helpers/image_processor.py` - Image preprocessing caching

Cache keys are generated using content hashes to ensure consistency across requests for the same data.
