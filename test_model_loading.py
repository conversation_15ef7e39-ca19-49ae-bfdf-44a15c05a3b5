#!/usr/bin/env python3
"""
Test script to verify model loading optimizations.
This script tests the face detector initialization and performance.
"""

import time
import numpy as np
from app.helpers.face_detector import FaceDetector, preload_face_detector, get_face_detector_status


def test_model_loading_performance():
    """Test model loading performance with different configurations."""
    print("=== Face Detection Model Loading Performance Test ===\n")
    
    # Test 1: Cold start (no preloading)
    print("Test 1: Cold start initialization")
    start_time = time.time()
    detector_cold = FaceDetector(preload=False)
    
    # Create a test image
    test_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    
    # First detection (triggers model loading)
    detection_start = time.time()
    has_faces, rectangles, error = detector_cold.detect_faces(test_image)
    detection_time = time.time() - detection_start
    
    print(f"  - Cold start detection time: {detection_time:.2f}s")
    print(f"  - Detection result: has_faces={has_faces}, error={error}")
    print(f"  - Status: {detector_cold.get_initialization_status()}")
    
    # Test 2: Preloaded model
    print("\nTest 2: Preloaded model initialization")
    preload_start = time.time()
    detector_preloaded = FaceDetector(preload=True)
    preload_time = time.time() - preload_start
    
    # Detection with preloaded model
    detection_start = time.time()
    has_faces, rectangles, error = detector_preloaded.detect_faces(test_image)
    detection_time = time.time() - detection_start
    
    print(f"  - Preload time: {preload_time:.2f}s")
    print(f"  - Preloaded detection time: {detection_time:.2f}s")
    print(f"  - Detection result: has_faces={has_faces}, error={error}")
    print(f"  - Status: {detector_preloaded.get_initialization_status()}")
    
    # Test 3: Global instance management
    print("\nTest 3: Global instance management")
    status_before = get_face_detector_status()
    print(f"  - Status before preload: {status_before}")
    
    preload_success = preload_face_detector()
    print(f"  - Preload success: {preload_success}")
    
    status_after = get_face_detector_status()
    print(f"  - Status after preload: {status_after}")
    
    # Test 4: Multiple detections (should be fast)
    print("\nTest 4: Multiple detections performance")
    detection_times = []
    for i in range(5):
        test_img = np.random.randint(0, 255, (128, 128, 3), dtype=np.uint8)
        start = time.time()
        has_faces, _, error = detector_preloaded.detect_faces(test_img)
        detection_times.append(time.time() - start)
    
    avg_time = sum(detection_times) / len(detection_times)
    print(f"  - Average detection time (5 runs): {avg_time:.3f}s")
    print(f"  - Individual times: {[f'{t:.3f}s' for t in detection_times]}")
    
    print("\n=== Test Complete ===")


def test_thread_safety():
    """Test thread safety of model initialization."""
    import threading
    import concurrent.futures
    
    print("\n=== Thread Safety Test ===")
    
    def worker_function(worker_id):
        """Worker function for thread safety test."""
        detector = FaceDetector(preload=False)
        test_image = np.random.randint(0, 255, (64, 64, 3), dtype=np.uint8)
        
        start_time = time.time()
        has_faces, _, error = detector.detect_faces(test_image)
        detection_time = time.time() - start_time
        
        return {
            'worker_id': worker_id,
            'detection_time': detection_time,
            'has_faces': has_faces,
            'error': error,
            'is_ready': detector.is_ready()
        }
    
    # Run multiple workers concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(worker_function, i) for i in range(3)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    print("Thread safety test results:")
    for result in results:
        print(f"  Worker {result['worker_id']}: "
              f"time={result['detection_time']:.3f}s, "
              f"ready={result['is_ready']}, "
              f"error={result['error']}")
    
    print("=== Thread Safety Test Complete ===")


if __name__ == "__main__":
    try:
        test_model_loading_performance()
        test_thread_safety()
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
