import os

from dotenv import find_dotenv, load_dotenv

try:
    load_dotenv(find_dotenv(".env"))
    load_dotenv(find_dotenv("thisversion.env"))
except Exception as e:
    pass

# SERVICE CONFIG
TITLE = "Image Facial Validation Service"
DESCRIPTION = "API for validating whether an image has faces or not."

ROOT_PATH = os.getenv("ROOT_PATH", "")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

VERSION = os.getenv("VERSION", "0.0.0")
BUILD_DATE = os.getenv("TIMESTAMP", "0")
ENVIRONMENT = os.getenv("ENVIRONMENT", "development").lower()


# API CONFIG
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", 8000))
DEBUG_MODULES = os.getenv(
    "DEBUG_MODULES", "app.api,app.services,watchfiles.main"
).split(",")

# FACE DETECTION CONFIG
PRELOAD_MODEL = os.getenv("PRELOAD_MODEL", "true").lower() == "true"
MODEL_DETECTION_SIZE = int(os.getenv("MODEL_DETECTION_SIZE", "640"))
MODEL_WARMUP_ENABLED = os.getenv("MODEL_WARMUP_ENABLED", "true").lower() == "true"

# IMAGE PROCESSING CONFIG
MAX_IMAGE_DIMENSION = int(os.getenv("MAX_IMAGE_DIMENSION", "1024"))
IMAGE_RESIZE_ENABLED = os.getenv("IMAGE_RESIZE_ENABLED", "true").lower() == "true"
IMAGE_QUALITY_OPTIMIZATION = (
    os.getenv("IMAGE_QUALITY_OPTIMIZATION", "true").lower() == "true"
)
PRESERVE_ASPECT_RATIO = os.getenv("PRESERVE_ASPECT_RATIO", "true").lower() == "true"
ULTRA_FAST_PROCESSING = os.getenv("ULTRA_FAST_PROCESSING", "true").lower() == "true"

# CACHING CONFIG
CACHE_ENABLED = os.getenv("CACHE_ENABLED", "true").lower() == "true"
CACHE_TYPE = os.getenv("CACHE_TYPE", "memory")  # memory, disk, or hybrid
CACHE_MAX_SIZE_MB = int(os.getenv("CACHE_MAX_SIZE_MB", "500"))
CACHE_TTL_SECONDS = int(os.getenv("CACHE_TTL_SECONDS", "3600"))  # 1 hour default
CACHE_IMAGE_DOWNLOADS = os.getenv("CACHE_IMAGE_DOWNLOADS", "true").lower() == "true"
CACHE_FACE_DETECTION = os.getenv("CACHE_FACE_DETECTION", "true").lower() == "true"
CACHE_IMAGE_PROCESSING = os.getenv("CACHE_IMAGE_PROCESSING", "true").lower() == "true"
CACHE_DIRECTORY = os.getenv("CACHE_DIRECTORY", "./cache")
