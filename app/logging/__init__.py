import inspect
import logging
import sys
from typing import Any

from loguru import logger as _logger

from app.config import DEBUG_MODULES, LOG_LEVEL

logger = _logger


class InterceptHandler(logging.Handler):
    def emit(self, record: logging.LogRecord) -> None:
        # Get corresponding Loguru level if it exists.
        level: str | int
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message.
        frame, depth = inspect.currentframe(), 0
        while frame and (depth == 0 or frame.f_code.co_filename == logging.__file__):
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


class EndpointFilter(logging.Filter):
    def __init__(self, path: str, *args: Any, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._path = path

    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find(self._path) == -1


def init_logger():
    # Intercept standard logging and redirect to Loguru
    logging.root.handlers = [InterceptHandler()]
    logging.root.setLevel(LOG_LEVEL)

    # Remove all log handlers and propagate to root logger
    for name in logging.root.manager.loggerDict.keys():
        logging.getLogger(name).handlers = []
        logging.getLogger(name).propagate = True

    if LOG_LEVEL != "DEBUG":
        uvicorn_access_logger = logging.getLogger("uvicorn.access")
        uvicorn_access_logger.addFilter(EndpointFilter(path="/metrics"))

    _logger.remove()
    _logger.add(
        "logs/some_api.log",
        rotation="10 MB",
        retention="1 days",
        level=LOG_LEVEL,
        encoding="utf-8",
    )
    _logger.add(sys.stderr, level=LOG_LEVEL, colorize=True)
    if LOG_LEVEL != "DEBUG":
        _logger.add(
            sys.stderr,
            level="DEBUG",
            colorize=True,
            filter=lambda record: record["level"].name == "DEBUG"
            and record["name"] in DEBUG_MODULES,
        )
