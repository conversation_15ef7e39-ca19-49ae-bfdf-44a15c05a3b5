import requests
from typing import <PERSON><PERSON>
from urllib.parse import urlparse

from app.logging import logger


def is_valid_image_url(url: str, timeout: int = 10) -> Tuple[bool, str | None]:
    """
    Validate if a URL is accessible and points to an image.
    
    Args:
        url: The URL to validate
        timeout: Request timeout in seconds
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Parse URL to check basic structure
        parsed = urlparse(url)
        if not all([parsed.scheme, parsed.netloc]):
            return False, "Invalid URL format"
        
        # Check if URL is accessible with HEAD request first
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        
        # If HEAD is not allowed, try GET with range header
        if response.status_code == 405:
            headers = {'Range': 'bytes=0-1023'}  # Only get first 1KB
            response = requests.get(url, headers=headers, timeout=timeout, allow_redirects=True)
        
        if response.status_code not in [200, 206, 416]:  # 416 = Range Not Satisfiable (but file exists)
            return False, f"HTTP {response.status_code}: Unable to access URL"
        
        # Check content type
        content_type = response.headers.get('content-type', '').lower()
        if content_type and not content_type.startswith('image/'):
            return False, f"URL does not point to an image (content-type: {content_type})"
        
        return True, None
        
    except requests.exceptions.Timeout:
        return False, "Request timeout"
    except requests.exceptions.ConnectionError:
        return False, "Connection error"
    except requests.exceptions.RequestException as e:
        return False, f"Request error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error validating URL {url}: {str(e)}")
        return False, f"Validation error: {str(e)}"
