import hashlib
import os
import pickle
import time
from typing import Any, Dict, Optional, <PERSON><PERSON>
from threading import Lock
import json

from app.config import (
    CACHE_ENABLED,
    CACHE_TYPE,
    CACHE_MAX_SIZE_MB,
    CACHE_TTL_SECONDS,
    CACHE_DIRECTORY,
)
from app.logging import logger


class CacheEntry:
    """Represents a cache entry with data, timestamp, and metadata."""
    
    def __init__(self, data: Any, size_bytes: int = 0, metadata: Optional[Dict] = None):
        self.data = data
        self.timestamp = time.time()
        self.size_bytes = size_bytes
        self.metadata = metadata or {}
        self.access_count = 0
        self.last_access = self.timestamp
    
    def is_expired(self, ttl_seconds: int) -> bool:
        """Check if the cache entry has expired."""
        return time.time() - self.timestamp > ttl_seconds
    
    def touch(self):
        """Update access statistics."""
        self.access_count += 1
        self.last_access = time.time()


class MemoryCache:
    """In-memory cache with LRU eviction."""
    
    def __init__(self, max_size_mb: int, ttl_seconds: int):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, CacheEntry] = {}
        self.current_size_bytes = 0
        self.lock = Lock()
    
    def _estimate_size(self, data: Any) -> int:
        """Estimate the size of data in bytes."""
        try:
            if hasattr(data, 'nbytes'):  # numpy arrays
                return data.nbytes
            elif isinstance(data, (str, bytes)):
                return len(data)
            else:
                # Fallback: use pickle size estimation
                return len(pickle.dumps(data))
        except Exception:
            return 1024  # Default estimate
    
    def _evict_expired(self):
        """Remove expired entries."""
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry.is_expired(self.ttl_seconds)
        ]
        for key in expired_keys:
            self._remove_entry(key)
    
    def _evict_lru(self, needed_bytes: int):
        """Evict least recently used entries to make space."""
        if not self.cache:
            return
        
        # Sort by last access time (oldest first)
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_access
        )
        
        freed_bytes = 0
        for key, entry in sorted_entries:
            if freed_bytes >= needed_bytes:
                break
            freed_bytes += entry.size_bytes
            self._remove_entry(key)
    
    def _remove_entry(self, key: str):
        """Remove an entry from cache."""
        if key in self.cache:
            entry = self.cache.pop(key)
            self.current_size_bytes -= entry.size_bytes
    
    def get(self, key: str) -> Optional[Any]:
        """Get data from cache."""
        with self.lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            if entry.is_expired(self.ttl_seconds):
                self._remove_entry(key)
                return None
            
            entry.touch()
            return entry.data
    
    def put(self, key: str, data: Any, metadata: Optional[Dict] = None) -> bool:
        """Put data into cache."""
        with self.lock:
            size_bytes = self._estimate_size(data)
            
            # Check if data is too large for cache
            if size_bytes > self.max_size_bytes:
                logger.warning(f"Data too large for cache: {size_bytes} bytes")
                return False
            
            # Remove existing entry if present
            if key in self.cache:
                self._remove_entry(key)
            
            # Clean up expired entries
            self._evict_expired()
            
            # Make space if needed
            needed_space = size_bytes
            if self.current_size_bytes + needed_space > self.max_size_bytes:
                self._evict_lru(needed_space)
            
            # Add new entry
            entry = CacheEntry(data, size_bytes, metadata)
            self.cache[key] = entry
            self.current_size_bytes += size_bytes
            
            return True
    
    def clear(self):
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()
            self.current_size_bytes = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            return {
                "entries": len(self.cache),
                "size_mb": self.current_size_bytes / (1024 * 1024),
                "max_size_mb": self.max_size_bytes / (1024 * 1024),
                "utilization": self.current_size_bytes / self.max_size_bytes if self.max_size_bytes > 0 else 0,
            }


class DiskCache:
    """Disk-based cache with file storage."""
    
    def __init__(self, cache_dir: str, max_size_mb: int, ttl_seconds: int):
        self.cache_dir = cache_dir
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        self.lock = Lock()
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # Index file to track cache entries
        self.index_file = os.path.join(cache_dir, "cache_index.json")
        self.index = self._load_index()
    
    def _load_index(self) -> Dict[str, Dict]:
        """Load cache index from disk."""
        try:
            if os.path.exists(self.index_file):
                with open(self.index_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load cache index: {e}")
        return {}
    
    def _save_index(self):
        """Save cache index to disk."""
        try:
            with open(self.index_file, 'w') as f:
                json.dump(self.index, f)
        except Exception as e:
            logger.error(f"Failed to save cache index: {e}")
    
    def _get_file_path(self, key: str) -> str:
        """Get file path for cache key."""
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{safe_key}.cache")
    
    def get(self, key: str) -> Optional[Any]:
        """Get data from disk cache."""
        with self.lock:
            if key not in self.index:
                return None
            
            entry_info = self.index[key]
            
            # Check if expired
            if time.time() - entry_info['timestamp'] > self.ttl_seconds:
                self._remove_entry(key)
                return None
            
            # Load data from file
            file_path = self._get_file_path(key)
            try:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # Update access time
                entry_info['last_access'] = time.time()
                entry_info['access_count'] = entry_info.get('access_count', 0) + 1
                self._save_index()
                
                return data
            except Exception as e:
                logger.error(f"Failed to load cached data: {e}")
                self._remove_entry(key)
                return None
    
    def put(self, key: str, data: Any, metadata: Optional[Dict] = None) -> bool:
        """Put data into disk cache."""
        with self.lock:
            file_path = self._get_file_path(key)
            
            try:
                # Save data to file
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                
                # Get file size
                size_bytes = os.path.getsize(file_path)
                
                # Update index
                self.index[key] = {
                    'timestamp': time.time(),
                    'size_bytes': size_bytes,
                    'metadata': metadata or {},
                    'access_count': 0,
                    'last_access': time.time(),
                }
                
                self._save_index()
                return True
                
            except Exception as e:
                logger.error(f"Failed to cache data to disk: {e}")
                return False
    
    def _remove_entry(self, key: str):
        """Remove entry from disk cache."""
        if key in self.index:
            file_path = self._get_file_path(key)
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                logger.error(f"Failed to remove cache file: {e}")
            
            del self.index[key]
            self._save_index()
    
    def clear(self):
        """Clear all cache entries."""
        with self.lock:
            for key in list(self.index.keys()):
                self._remove_entry(key)


class CacheManager:
    """Main cache manager that handles different cache backends."""
    
    def __init__(self):
        self.enabled = CACHE_ENABLED
        self.cache = None
        
        if self.enabled:
            if CACHE_TYPE == "memory":
                self.cache = MemoryCache(CACHE_MAX_SIZE_MB, CACHE_TTL_SECONDS)
            elif CACHE_TYPE == "disk":
                self.cache = DiskCache(CACHE_DIRECTORY, CACHE_MAX_SIZE_MB, CACHE_TTL_SECONDS)
            else:
                logger.warning(f"Unknown cache type: {CACHE_TYPE}, disabling cache")
                self.enabled = False
        
        if self.enabled:
            logger.info(f"Cache initialized: type={CACHE_TYPE}, max_size={CACHE_MAX_SIZE_MB}MB, ttl={CACHE_TTL_SECONDS}s")
        else:
            logger.info("Caching disabled")
    
    def generate_key(self, *args, prefix: str = "") -> str:
        """Generate a cache key from arguments."""
        key_data = f"{prefix}:{':'.join(str(arg) for arg in args)}"
        return hashlib.sha256(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get data from cache."""
        if not self.enabled or not self.cache:
            return None
        return self.cache.get(key)
    
    def put(self, key: str, data: Any, metadata: Optional[Dict] = None) -> bool:
        """Put data into cache."""
        if not self.enabled or not self.cache:
            return False
        return self.cache.put(key, data, metadata)
    
    def clear(self):
        """Clear all cache entries."""
        if self.enabled and self.cache:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self.enabled or not self.cache:
            return {"enabled": False}
        
        stats = self.cache.get_stats()
        stats["enabled"] = True
        stats["type"] = CACHE_TYPE
        return stats


# Global cache manager instance
_cache_manager = None


def get_cache_manager() -> CacheManager:
    """Get the global cache manager instance."""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager
