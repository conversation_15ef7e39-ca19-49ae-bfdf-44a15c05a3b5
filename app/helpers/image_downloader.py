import io
import time
from typing import <PERSON><PERSON>

import numpy as np
import requests
from PIL import Image

from app.config import IMAGE_RESIZE_ENABLED, MAX_IMAGE_DIMENSION, ULTRA_FAST_PROCESSING
from app.logging import logger


def download_image_to_memory(
    url: str,
    timeout: int = 30,
    max_size_mb: int = 10,
    optimize_on_download: bool = True,
) -> Tuple[np.ndarray | None, str | None]:
    """
    Download an image from URL and load it into memory as numpy array with optional optimization.

    Args:
        url: The image URL to download
        timeout: Request timeout in seconds
        max_size_mb: Maximum file size in MB
        optimize_on_download: Whether to optimize image during download

    Returns:
        Tuple of (image_array, error_message)
        image_array is None if there was an error
    """
    try:
        logger.debug(f"Downloading image from: {url}")

        # Download the image
        response = requests.get(url, timeout=timeout, stream=True)
        response.raise_for_status()

        # Check content length if available
        content_length = response.headers.get("content-length")
        if content_length:
            size_mb = int(content_length) / (1024 * 1024)
            if size_mb > max_size_mb:
                return None, f"Image too large: {size_mb:.1f}MB (max: {max_size_mb}MB)"

        # Read image data into memory
        image_data = io.BytesIO()
        downloaded_size = 0
        max_size_bytes = max_size_mb * 1024 * 1024

        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                downloaded_size += len(chunk)
                if downloaded_size > max_size_bytes:
                    return (
                        None,
                        f"Image too large: exceeded {max_size_mb}MB during download",
                    )
                image_data.write(chunk)

        image_data.seek(0)

        # Load image using PIL
        try:
            pil_image = Image.open(image_data)
            # Convert to RGB if necessary (handles RGBA, grayscale, etc.)
            if pil_image.mode != "RGB":
                pil_image = pil_image.convert("RGB")

            # Optimize image size during download if enabled
            if optimize_on_download and IMAGE_RESIZE_ENABLED:
                original_size = pil_image.size
                width, height = original_size

                # Calculate optimal size
                if max(width, height) > MAX_IMAGE_DIMENSION:
                    from app.helpers.image_processor import ImageProcessor

                    new_width, new_height = ImageProcessor.calculate_optimal_size(
                        width, height
                    )

                    if (new_width, new_height) != (width, height):
                        # Only log significant optimizations
                        size_reduction = (
                            ((width * height) - (new_width * new_height))
                            / (width * height)
                            * 100
                        )
                        if size_reduction > 30:  # Log only if >30% size reduction
                            logger.info(
                                f"Optimizing large download: {width}x{height} -> {new_width}x{new_height}"
                            )

                        # Use ultra-fast resizing if enabled
                        if ULTRA_FAST_PROCESSING:
                            # Convert PIL to numpy, resize with OpenCV, convert back
                            img_array = np.array(pil_image)
                            resized_array = ImageProcessor.resize_image_ultra_fast(
                                img_array, new_width, new_height
                            )
                            pil_image = Image.fromarray(resized_array)
                        else:
                            pil_image = ImageProcessor.resize_image_pil(
                                pil_image, new_width, new_height
                            )

            # Convert PIL image to numpy array (RGB format)
            image_array = np.array(pil_image)

            # Log successful download with meaningful details
            height, width = image_array.shape[:2]
            size_mb = image_array.nbytes / (1024 * 1024)
            logger.info(f"Image loaded: {width}x{height} ({size_mb:.1f}MB)")
            return image_array, None

        except Exception as e:
            return None, f"Failed to load image: {str(e)}"

    except requests.exceptions.Timeout:
        return None, "Download timeout"
    except requests.exceptions.ConnectionError:
        return None, "Connection error during download"
    except requests.exceptions.HTTPError as e:
        return None, f"HTTP error: {e.response.status_code}"
    except requests.exceptions.RequestException as e:
        return None, f"Download error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error downloading image from {url}: {str(e)}")
        return None, f"Download error: {str(e)}"
