"""
Optimized image processing utilities for face detection.
Handles image resizing, color space conversion, and preprocessing optimizations.
"""

import time
from typing import Optional, <PERSON>ple

import cv2
import numpy as np
from PIL import Image

from app.config import (
    CACHE_IMAGE_PROCESSING,
    IMAGE_QUALITY_OPTIMIZATION,
    IMAGE_RESIZE_ENABLED,
    MAX_IMAGE_DIMENSION,
    PRESERVE_ASPECT_RATIO,
    ULTRA_FAST_PROCESSING,
)
from app.logging import logger


class ImageProcessor:
    """Ultra-fast image processor for face detection preprocessing."""

    @staticmethod
    def calculate_optimal_size(
        width: int, height: int, max_dimension: int = MAX_IMAGE_DIMENSION
    ) -> Tuple[int, int]:
        """
        Calculate optimal image size for face detection while preserving aspect ratio.

        Args:
            width: Original image width
            height: Original image height
            max_dimension: Maximum allowed dimension

        Returns:
            Tuple of (new_width, new_height)
        """
        if not IMAGE_RESIZE_ENABLED:
            return width, height

        # If image is already small enough, don't resize
        if max(width, height) <= max_dimension:
            return width, height

        if PRESERVE_ASPECT_RATIO:
            # Calculate scale factor to fit within max_dimension
            scale = max_dimension / max(width, height)
            new_width = int(width * scale)
            new_height = int(height * scale)

            # Ensure dimensions are even (better for some algorithms)
            new_width = new_width - (new_width % 2)
            new_height = new_height - (new_height % 2)

            return new_width, new_height
        else:
            # Square resize (may distort image but faster processing)
            return max_dimension, max_dimension

    @staticmethod
    def resize_image_pil(
        pil_image: Image.Image, target_width: int, target_height: int
    ) -> Image.Image:
        """
        Resize image using PIL with optimal resampling.

        Args:
            pil_image: PIL Image object
            target_width: Target width
            target_height: Target height

        Returns:
            Resized PIL Image
        """
        if IMAGE_QUALITY_OPTIMIZATION:
            # Use high-quality resampling for better face detection
            resample = Image.Resampling.LANCZOS
        else:
            # Use faster resampling
            resample = Image.Resampling.BILINEAR

        return pil_image.resize((target_width, target_height), resample)

    @staticmethod
    def resize_image_ultra_fast(
        image_array: np.ndarray, target_width: int, target_height: int
    ) -> np.ndarray:
        """
        Ultra-fast image resizing using OpenCV with speed-optimized settings.

        Args:
            image_array: NumPy array in RGB format
            target_width: Target width
            target_height: Target height

        Returns:
            Resized image array in RGB format
        """
        # Use fastest interpolation method
        interpolation = cv2.INTER_LINEAR

        # Direct resize without color conversion (saves time)
        return cv2.resize(
            image_array, (target_width, target_height), interpolation=interpolation
        )

    @staticmethod
    def rgb_to_bgr_ultra_fast(rgb_image: np.ndarray) -> np.ndarray:
        """
        Ultra-fast RGB to BGR conversion using numpy view.

        Args:
            rgb_image: Image array in RGB format

        Returns:
            Image array in BGR format (as view, not copy)
        """
        # Always use numpy slicing for maximum speed (creates view, not copy)
        return rgb_image[:, :, ::-1]

    @staticmethod
    def preprocess_ultra_fast(image_array: np.ndarray) -> Tuple[np.ndarray, dict]:
        """
        Ultra-fast preprocessing pipeline optimized for speed over quality.

        Args:
            image_array: Input image as numpy array (RGB format)

        Returns:
            Tuple of (bgr_image_ready_for_detection, processing_info)
        """
        start_time = time.time()

        height, width = image_array.shape[:2]
        processing_info = {
            "original_shape": image_array.shape,
            "resized": False,
            "method": "ultra_fast",
        }

        # Step 1: Quick resize check and resize if needed
        new_width, new_height = ImageProcessor.calculate_optimal_size(width, height)

        if (new_width, new_height) != (width, height):
            # Ultra-fast resize
            resized_rgb = ImageProcessor.resize_image_ultra_fast(
                image_array, new_width, new_height
            )
            processing_info.update(
                {
                    "resized": True,
                    "new_shape": resized_rgb.shape,
                }
            )
        else:
            resized_rgb = image_array

        # Step 2: Ultra-fast color conversion
        bgr_image = ImageProcessor.rgb_to_bgr_ultra_fast(resized_rgb)

        total_time = time.time() - start_time
        processing_info["total_processing_time"] = total_time

        # Only log if processing took noticeable time
        if total_time > 0.05:
            logger.debug(f"Ultra-fast preprocessing: {total_time:.3f}s")

        return bgr_image, processing_info

    @staticmethod
    def resize_image_cv2(
        image_array: np.ndarray, target_width: int, target_height: int
    ) -> np.ndarray:
        """
        Resize image using OpenCV with optimal interpolation.

        Args:
            image_array: NumPy array in RGB format
            target_width: Target width
            target_height: Target height

        Returns:
            Resized image array in RGB format
        """
        if IMAGE_QUALITY_OPTIMIZATION:
            # Use high-quality interpolation
            interpolation = cv2.INTER_LANCZOS4
        else:
            # Use faster interpolation
            interpolation = cv2.INTER_LINEAR

        # OpenCV expects BGR, so convert RGB -> BGR -> resize -> BGR -> RGB
        bgr_image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        resized_bgr = cv2.resize(
            bgr_image, (target_width, target_height), interpolation=interpolation
        )
        return cv2.cvtColor(resized_bgr, cv2.COLOR_BGR2RGB)

    @staticmethod
    def optimize_for_face_detection(image_array: np.ndarray) -> Tuple[np.ndarray, dict]:
        """
        Optimize image for face detection processing.

        Args:
            image_array: Input image as numpy array (RGB format)

        Returns:
            Tuple of (optimized_image, processing_info)
        """
        start_time = time.time()
        processing_info = {
            "original_shape": image_array.shape,
            "resized": False,
            "resize_method": None,
            "processing_time": 0.0,
        }

        height, width = image_array.shape[:2]

        # Calculate optimal size
        new_width, new_height = ImageProcessor.calculate_optimal_size(width, height)

        # Resize if needed
        if (new_width, new_height) != (width, height):
            # Only log significant resizing operations (>20% size reduction)
            size_reduction = (
                ((width * height) - (new_width * new_height)) / (width * height) * 100
            )
            if size_reduction > 20:
                logger.info(
                    f"Resizing large image: {width}x{height} -> {new_width}x{new_height} ({size_reduction:.1f}% reduction)"
                )

            # Use CV2 for resizing as it's generally faster for numpy arrays
            optimized_image = ImageProcessor.resize_image_cv2(
                image_array, new_width, new_height
            )

            processing_info.update(
                {
                    "resized": True,
                    "resize_method": "cv2",
                    "new_shape": optimized_image.shape,
                    "size_reduction_percent": size_reduction,
                }
            )
        else:
            optimized_image = image_array

        processing_info["processing_time"] = time.time() - start_time

        return optimized_image, processing_info

    @staticmethod
    def rgb_to_bgr_optimized(rgb_image: np.ndarray) -> np.ndarray:
        """
        Optimized RGB to BGR conversion for face detection.

        Args:
            rgb_image: Image array in RGB format

        Returns:
            Image array in BGR format
        """
        # Method 1: Use OpenCV (fastest for large images)
        if rgb_image.size > 1024 * 1024:  # For images larger than 1MP
            return cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)

        # Method 2: Use numpy array slicing (faster for smaller images)
        # This creates a view, not a copy, so it's memory efficient
        return rgb_image[:, :, ::-1]

    @staticmethod
    def preprocess_for_detection(image_array: np.ndarray) -> Tuple[np.ndarray, dict]:
        """
        Complete preprocessing pipeline for face detection.

        Args:
            image_array: Input image as numpy array (RGB format)

        Returns:
            Tuple of (bgr_image_ready_for_detection, processing_info)
        """
        # Import here to avoid circular imports
        import hashlib

        from .cache_manager import get_cache_manager

        # Initialize cache variables
        cache_manager = None
        cache_key = None
        image_hash = None

        # Set up caching if enabled
        if CACHE_IMAGE_PROCESSING:
            cache_manager = get_cache_manager()
            image_hash = hashlib.sha256(image_array.tobytes()).hexdigest()
            cache_key = cache_manager.generate_key(
                image_hash,
                image_array.shape,
                ULTRA_FAST_PROCESSING,
                prefix="image_preprocessing",
            )

            # Check cache first
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug("Preprocessed image loaded from cache")
                return cached_result

        # Choose processing method based on configuration
        if ULTRA_FAST_PROCESSING:
            result = ImageProcessor.preprocess_ultra_fast(image_array)
        else:
            # Fallback to standard processing
            start_time = time.time()

            # Step 1: Optimize image size
            optimized_rgb, resize_info = ImageProcessor.optimize_for_face_detection(
                image_array
            )

            # Step 2: Convert to BGR for InsightFace
            bgr_image = ImageProcessor.rgb_to_bgr_optimized(optimized_rgb)

            # Compile processing information
            total_time = time.time() - start_time
            processing_info = {
                **resize_info,
                "color_conversion_time": total_time - resize_info["processing_time"],
                "total_processing_time": total_time,
                "memory_reduction": (
                    (image_array.nbytes - bgr_image.nbytes) / image_array.nbytes * 100
                    if resize_info["resized"]
                    else 0.0
                ),
            }

            # Only log if processing took significant time or memory was saved
            if total_time > 0.1 or processing_info.get("memory_reduction", 0) > 10:
                logger.info(
                    f"Image preprocessing: {total_time:.3f}s, memory reduction: {processing_info['memory_reduction']:.1f}%"
                )

            result = (bgr_image, processing_info)

        # Cache the result if caching is enabled
        if (
            CACHE_IMAGE_PROCESSING
            and cache_manager is not None
            and cache_key is not None
        ):
            cache_manager.put(
                cache_key,
                result,
                metadata={
                    "image_shape": image_array.shape,
                    "ultra_fast": ULTRA_FAST_PROCESSING,
                    "image_hash": image_hash[:16] if image_hash else "unknown",
                },
            )
            logger.debug("Cached preprocessed image")

        return result


# Convenience functions for backward compatibility
def optimize_image_for_detection(image_array: np.ndarray) -> Tuple[np.ndarray, dict]:
    """Convenience function for image optimization."""
    return ImageProcessor.preprocess_for_detection(image_array)


def convert_rgb_to_bgr(rgb_image: np.ndarray) -> np.ndarray:
    """Convenience function for color conversion."""
    return ImageProcessor.rgb_to_bgr_optimized(rgb_image)
