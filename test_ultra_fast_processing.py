#!/usr/bin/env python3
"""
Test script to benchmark ultra-fast image processing optimizations.
Compares standard vs ultra-fast processing methods.
"""

import time
import numpy as np
import os

# Set ultra-fast processing for testing
os.environ["ULTRA_FAST_PROCESSING"] = "true"

from app.helpers.image_processor import ImageProcessor


def create_test_images():
    """Create test images of various sizes."""
    return {
        'small': np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8),
        'medium': np.random.randint(0, 255, (1024, 768, 3), dtype=np.uint8),
        'large': np.random.randint(0, 255, (2048, 1536, 3), dtype=np.uint8),
        'very_large': np.random.randint(0, 255, (4000, 3000, 3), dtype=np.uint8),
    }


def benchmark_resizing():
    """Benchmark different resizing methods."""
    print("=== Resizing Performance Benchmark ===")
    
    test_images = create_test_images()
    
    for name, image in test_images.items():
        height, width = image.shape[:2]
        target_width, target_height = 1024, 768
        
        print(f"\n{name.upper()} Image ({width}x{height}):")
        
        # Test standard OpenCV resize
        start_time = time.time()
        resized_standard = ImageProcessor.resize_image_cv2(image, target_width, target_height)
        standard_time = time.time() - start_time
        
        # Test ultra-fast resize
        start_time = time.time()
        resized_ultra_fast = ImageProcessor.resize_image_ultra_fast(image, target_width, target_height)
        ultra_fast_time = time.time() - start_time
        
        speedup = standard_time / ultra_fast_time if ultra_fast_time > 0 else float('inf')
        
        print(f"  Standard resize: {standard_time:.4f}s")
        print(f"  Ultra-fast resize: {ultra_fast_time:.4f}s")
        print(f"  Speedup: {speedup:.2f}x")
        
        # Verify output shapes are the same
        if resized_standard.shape == resized_ultra_fast.shape:
            print(f"  ✓ Output shapes match: {resized_ultra_fast.shape}")
        else:
            print(f"  ✗ Shape mismatch!")


def benchmark_color_conversion():
    """Benchmark color conversion methods."""
    print("\n=== Color Conversion Performance Benchmark ===")
    
    test_images = create_test_images()
    
    for name, image in test_images.items():
        height, width = image.shape[:2]
        print(f"\n{name.upper()} Image ({width}x{height}):")
        
        # Test standard conversion
        start_time = time.time()
        bgr_standard = ImageProcessor.rgb_to_bgr_optimized(image)
        standard_time = time.time() - start_time
        
        # Test ultra-fast conversion
        start_time = time.time()
        bgr_ultra_fast = ImageProcessor.rgb_to_bgr_ultra_fast(image)
        ultra_fast_time = time.time() - start_time
        
        speedup = standard_time / ultra_fast_time if ultra_fast_time > 0 else float('inf')
        
        print(f"  Standard conversion: {standard_time:.4f}s")
        print(f"  Ultra-fast conversion: {ultra_fast_time:.4f}s")
        print(f"  Speedup: {speedup:.2f}x")
        
        # Verify results are identical
        if np.array_equal(bgr_standard, bgr_ultra_fast):
            print(f"  ✓ Results are identical")
        else:
            print(f"  ✗ Results differ!")


def benchmark_complete_pipeline():
    """Benchmark complete preprocessing pipelines."""
    print("\n=== Complete Pipeline Performance Benchmark ===")
    
    test_images = create_test_images()
    
    for name, image in test_images.items():
        height, width = image.shape[:2]
        print(f"\n{name.upper()} Image ({width}x{height}):")
        
        # Test standard pipeline
        start_time = time.time()
        bgr_standard, info_standard = ImageProcessor.optimize_for_face_detection(image)
        bgr_standard = ImageProcessor.rgb_to_bgr_optimized(bgr_standard)
        standard_time = time.time() - start_time
        
        # Test ultra-fast pipeline
        start_time = time.time()
        bgr_ultra_fast, info_ultra_fast = ImageProcessor.preprocess_ultra_fast(image)
        ultra_fast_time = time.time() - start_time
        
        speedup = standard_time / ultra_fast_time if ultra_fast_time > 0 else float('inf')
        
        print(f"  Standard pipeline: {standard_time:.4f}s")
        print(f"  Ultra-fast pipeline: {ultra_fast_time:.4f}s")
        print(f"  Speedup: {speedup:.2f}x")
        
        # Show processing details
        print(f"  Standard info: {info_standard}")
        print(f"  Ultra-fast info: {info_ultra_fast}")


def benchmark_memory_usage():
    """Benchmark memory usage patterns."""
    print("\n=== Memory Usage Benchmark ===")
    
    # Create a large image
    large_image = np.random.randint(0, 255, (3000, 2000, 3), dtype=np.uint8)
    original_size_mb = large_image.nbytes / (1024 * 1024)
    
    print(f"Original image: {large_image.shape} ({original_size_mb:.1f}MB)")
    
    # Test ultra-fast processing
    start_time = time.time()
    processed_image, info = ImageProcessor.preprocess_ultra_fast(large_image)
    processing_time = time.time() - start_time
    
    processed_size_mb = processed_image.nbytes / (1024 * 1024)
    memory_reduction = ((original_size_mb - processed_size_mb) / original_size_mb) * 100
    
    print(f"Processed image: {processed_image.shape} ({processed_size_mb:.1f}MB)")
    print(f"Processing time: {processing_time:.4f}s")
    print(f"Memory reduction: {memory_reduction:.1f}%")
    print(f"Processing info: {info}")


def stress_test():
    """Stress test with multiple iterations."""
    print("\n=== Stress Test (100 iterations) ===")
    
    # Create a medium-sized image
    test_image = np.random.randint(0, 255, (1500, 1200, 3), dtype=np.uint8)
    
    # Ultra-fast processing stress test
    times = []
    for i in range(100):
        start_time = time.time()
        _, _ = ImageProcessor.preprocess_ultra_fast(test_image)
        times.append(time.time() - start_time)
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"100 iterations of ultra-fast processing:")
    print(f"  Average time: {avg_time:.4f}s")
    print(f"  Min time: {min_time:.4f}s")
    print(f"  Max time: {max_time:.4f}s")
    print(f"  Total time: {sum(times):.2f}s")
    print(f"  Throughput: {100/sum(times):.1f} images/second")


if __name__ == "__main__":
    try:
        print("Ultra-Fast Image Processing Performance Test")
        print("=" * 50)
        
        benchmark_resizing()
        benchmark_color_conversion()
        benchmark_complete_pipeline()
        benchmark_memory_usage()
        stress_test()
        
        print("\n" + "=" * 50)
        print("Performance test completed!")
        print("\nTo enable ultra-fast processing in production:")
        print("export ULTRA_FAST_PROCESSING=true")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
