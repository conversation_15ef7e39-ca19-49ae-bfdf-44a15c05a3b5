#!/usr/bin/env python3
"""
Test script to verify image processing optimizations.
Tests image resizing, color conversion, and overall performance improvements.
"""

import time
import numpy as np
from PIL import Image

from app.helpers.image_processor import ImageProcessor, optimize_image_for_detection, convert_rgb_to_bgr
from app.helpers.image_downloader import download_image_to_memory


def create_test_images():
    """Create test images of various sizes for performance testing."""
    test_images = {}
    
    # Small image (should not be resized)
    test_images['small'] = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    
    # Medium image (might be resized depending on config)
    test_images['medium'] = np.random.randint(0, 255, (800, 600, 3), dtype=np.uint8)
    
    # Large image (should be resized)
    test_images['large'] = np.random.randint(0, 255, (2048, 1536, 3), dtype=np.uint8)
    
    # Very large image (should definitely be resized)
    test_images['very_large'] = np.random.randint(0, 255, (4000, 3000, 3), dtype=np.uint8)
    
    return test_images


def test_image_resizing():
    """Test image resizing functionality."""
    print("=== Image Resizing Test ===")
    
    test_images = create_test_images()
    
    for name, image in test_images.items():
        height, width = image.shape[:2]
        print(f"\nTesting {name} image ({width}x{height}):")
        
        # Test optimal size calculation
        new_width, new_height = ImageProcessor.calculate_optimal_size(width, height)
        print(f"  Optimal size: {new_width}x{new_height}")
        
        # Test image optimization
        start_time = time.time()
        optimized_image, info = ImageProcessor.optimize_for_face_detection(image)
        optimization_time = time.time() - start_time
        
        print(f"  Optimization time: {optimization_time:.3f}s")
        print(f"  Processing info: {info}")
        
        if info['resized']:
            memory_saved = (image.nbytes - optimized_image.nbytes) / (1024 * 1024)
            print(f"  Memory saved: {memory_saved:.2f} MB")


def test_color_conversion():
    """Test color conversion optimizations."""
    print("\n=== Color Conversion Test ===")
    
    test_images = create_test_images()
    
    for name, image in test_images.items():
        print(f"\nTesting {name} image color conversion:")
        
        # Test old method (simple array slicing)
        start_time = time.time()
        bgr_old = image[:, :, ::-1]
        old_time = time.time() - start_time
        
        # Test optimized method
        start_time = time.time()
        bgr_optimized = ImageProcessor.rgb_to_bgr_optimized(image)
        optimized_time = time.time() - start_time
        
        print(f"  Old method time: {old_time:.4f}s")
        print(f"  Optimized method time: {optimized_time:.4f}s")
        print(f"  Speed improvement: {old_time/optimized_time:.2f}x")
        
        # Verify results are the same
        if np.array_equal(bgr_old, bgr_optimized):
            print("  ✓ Results are identical")
        else:
            print("  ✗ Results differ!")


def test_complete_preprocessing():
    """Test complete preprocessing pipeline."""
    print("\n=== Complete Preprocessing Pipeline Test ===")
    
    test_images = create_test_images()
    
    for name, image in test_images.items():
        print(f"\nTesting {name} image complete preprocessing:")
        
        # Test complete preprocessing
        start_time = time.time()
        bgr_image, processing_info = ImageProcessor.preprocess_for_detection(image)
        total_time = time.time() - start_time
        
        print(f"  Total preprocessing time: {total_time:.3f}s")
        print(f"  Processing details: {processing_info}")
        
        # Verify output format
        if len(bgr_image.shape) == 3 and bgr_image.shape[2] == 3:
            print("  ✓ Output format is correct (3-channel)")
        else:
            print("  ✗ Output format is incorrect")


def test_real_image_download():
    """Test with real image download and processing."""
    print("\n=== Real Image Download and Processing Test ===")
    
    # Test URLs with different image sizes
    test_urls = [
        "https://picsum.photos/400/300",  # Small image
        "https://picsum.photos/1200/800",  # Medium image
        "https://picsum.photos/2400/1600",  # Large image
    ]
    
    for i, url in enumerate(test_urls):
        print(f"\nTesting real image download {i+1}:")
        print(f"  URL: {url}")
        
        try:
            # Download with optimization enabled
            start_time = time.time()
            image_optimized, error = download_image_to_memory(url, optimize_on_download=True)
            download_time_optimized = time.time() - start_time
            
            if image_optimized is not None:
                print(f"  Download time (optimized): {download_time_optimized:.3f}s")
                print(f"  Final image shape: {image_optimized.shape}")
                
                # Test face detection preprocessing
                start_time = time.time()
                bgr_image, processing_info = optimize_image_for_detection(image_optimized)
                preprocessing_time = time.time() - start_time
                
                print(f"  Additional preprocessing time: {preprocessing_time:.3f}s")
                print(f"  Processing info: {processing_info}")
            else:
                print(f"  Download failed: {error}")
                
        except Exception as e:
            print(f"  Test failed: {e}")


def benchmark_performance():
    """Benchmark performance improvements."""
    print("\n=== Performance Benchmark ===")
    
    # Create a large test image
    large_image = np.random.randint(0, 255, (2000, 1500, 3), dtype=np.uint8)
    print(f"Benchmarking with {large_image.shape} image")
    
    # Benchmark old approach (no optimization)
    print("\nOld approach (no optimization):")
    start_time = time.time()
    bgr_old = large_image[:, :, ::-1]  # Simple color conversion
    old_total_time = time.time() - start_time
    print(f"  Time: {old_total_time:.3f}s")
    print(f"  Memory usage: {bgr_old.nbytes / (1024*1024):.2f} MB")
    
    # Benchmark new approach (with optimization)
    print("\nNew approach (with optimization):")
    start_time = time.time()
    bgr_optimized, processing_info = ImageProcessor.preprocess_for_detection(large_image)
    new_total_time = time.time() - start_time
    print(f"  Time: {new_total_time:.3f}s")
    print(f"  Memory usage: {bgr_optimized.nbytes / (1024*1024):.2f} MB")
    print(f"  Processing details: {processing_info}")
    
    # Calculate improvements
    if processing_info.get('resized', False):
        memory_reduction = processing_info.get('memory_reduction', 0)
        print(f"\nImprovements:")
        print(f"  Memory reduction: {memory_reduction:.1f}%")
        print(f"  Processing time: {new_total_time:.3f}s")
    else:
        print(f"\nNo resizing needed for this image size")


if __name__ == "__main__":
    try:
        test_image_resizing()
        test_color_conversion()
        test_complete_preprocessing()
        test_real_image_download()
        benchmark_performance()
        print("\n=== All Tests Complete ===")
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
